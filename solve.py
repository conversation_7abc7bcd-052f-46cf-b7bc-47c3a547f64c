#!/usr/bin/env python3

import socket
import time
from mt19937predictor import MT19937Predictor

def connect_to_server(host, port):
    """Connect to the CTF server"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.connect((host, port))
    return sock

def receive_until(sock, delimiter=b'\n'):
    """Receive data until delimiter"""
    data = b''
    while True:
        chunk = sock.recv(1)
        if not chunk:
            break
        data += chunk
        if data.endswith(delimiter):
            break
    return data

def send_command(sock, command):
    """Send a command to the server"""
    sock.send(command.encode() + b'\n')

def extract_number(response):
    """Extract the random number from server response"""
    # Response format: "Следующее число: 123456789"
    response_str = response.decode('utf-8', errors='ignore')
    if "число:" in response_str:
        # Find the number after "число:"
        parts = response_str.split("число:")
        if len(parts) > 1:
            number_str = parts[1].strip()
            # Extract just the number (remove any trailing text)
            number = ''
            for char in number_str:
                if char.isdigit():
                    number += char
                else:
                    break
            if number:
                return int(number)
    return None

def solve_prng_challenge():
    """Main function to solve the PRNG challenge"""
    host = "ctf.mf.grsu.by"
    port = 9045
    
    print(f"Connecting to {host}:{port}...")
    sock = connect_to_server(host, port)
    
    try:
        # Read initial message
        initial_msg = receive_until(sock, b'> ')
        print("Initial message received:")
        print(initial_msg.decode('utf-8', errors='ignore'))
        
        # Initialize the predictor
        predictor = MT19937Predictor()
        collected_numbers = []
        
        print("Collecting random numbers...")
        
        # Collect 625 random numbers (624 + 1 extra to help with prediction)
        for i in range(625):
            print(f"Collecting number {i+1}/625...")
            
            # Send command to get next number
            send_command(sock, "1")
            
            # Receive response
            response = receive_until(sock, b'> ')
            
            # Extract the number
            number = extract_number(response)
            if number is not None:
                print(f"Got number: {number}")
                collected_numbers.append(number)
                
                # Feed the number to the predictor
                # getrandbits(31) = genrand_uint32() >> 1
                # So we need to left-shift by 1 to get back the original 32-bit value
                # However, we lose the LSB, so we need to try both possibilities
                predictor.setrandbits(number << 1, 32)
            else:
                print(f"Failed to extract number from response: {response}")
                break
        
        if len(collected_numbers) < 625:
            print(f"Only collected {len(collected_numbers)} numbers, need 625")
            return
        
        print(f"Successfully collected {len(collected_numbers)} numbers")
        print("Trying to predict next number...")

        # Since getrandbits(31) loses the LSB, we need to try different combinations
        # We'll try more systematic patterns for the missing LSBs
        success = False

        # Try different systematic approaches
        patterns_to_try = [
            lambda i: 0,  # All LSBs = 0
            lambda i: 1,  # All LSBs = 1
            lambda i: i & 1,  # Alternating 0,1,0,1...
            lambda i: (i >> 1) & 1,  # Pattern based on position
            lambda i: (i * 7) & 1,  # Pseudo-random pattern
            lambda i: ((i * 13) >> 2) & 1,  # Another pattern
            lambda i: (i % 3) & 1,  # Pattern based on mod 3
            lambda i: ((i ^ (i >> 1)) & 1),  # XOR pattern
        ]

        for attempt, pattern_func in enumerate(patterns_to_try):
            print(f"Attempt {attempt + 1}/{len(patterns_to_try)}...")

            # Create a new predictor for this attempt
            test_predictor = MT19937Predictor()

            # Feed the first 624 numbers with current LSB pattern
            for i in range(624):
                number = collected_numbers[i]
                lsb = pattern_func(i)
                reconstructed = (number << 1) | lsb
                test_predictor.setrandbits(reconstructed, 32)

            # Predict the next number
            predicted_32bit = test_predictor.getrandbits(32)
            predicted_31bit = predicted_32bit >> 1  # Convert to 31-bit like server

            # Check if this prediction matches the 625th number we collected
            if predicted_31bit == collected_numbers[624]:
                print(f"Found correct pattern! Predicted 625th number correctly.")

                # Now predict the actual next number (626th)
                next_predicted_32bit = test_predictor.getrandbits(32)
                next_predicted_31bit = next_predicted_32bit >> 1

                print(f"Predicted next number: {next_predicted_31bit}")

                # Send command to guess the number
                send_command(sock, "2")

                # Wait for prompt
                response = receive_until(sock, b': ')
                print("Server asking for guess...")

                # Send our prediction
                send_command(sock, str(next_predicted_31bit))

                # Get the result
                result = receive_until(sock, b'\n')
                print("Server response:")
                print(result.decode('utf-8', errors='ignore'))

                # Check if we got the flag
                if "Флаг:" in result.decode('utf-8', errors='ignore'):
                    print("SUCCESS! Got the flag!")
                    success = True
                else:
                    print("Failed to predict correctly")
                break
            else:
                print(f"Pattern {attempt + 1} didn't work, trying next...")

        if not success:
            print("All systematic patterns failed. Trying brute force on a subset...")

            # If systematic patterns fail, try brute force on first few bits
            for mask in range(16):  # Try 16 different patterns for first 4 positions
                print(f"Brute force attempt {mask + 1}/16...")

                test_predictor = MT19937Predictor()

                for i in range(624):
                    number = collected_numbers[i]
                    if i < 4:
                        lsb = (mask >> i) & 1
                    else:
                        lsb = 0  # Default to 0 for the rest
                    reconstructed = (number << 1) | lsb
                    test_predictor.setrandbits(reconstructed, 32)

                predicted_32bit = test_predictor.getrandbits(32)
                predicted_31bit = predicted_32bit >> 1

                if predicted_31bit == collected_numbers[624]:
                    print(f"Found correct brute force pattern! Predicted 625th number correctly.")

                    next_predicted_32bit = test_predictor.getrandbits(32)
                    next_predicted_31bit = next_predicted_32bit >> 1

                    print(f"Predicted next number: {next_predicted_31bit}")

                    send_command(sock, "2")
                    response = receive_until(sock, b': ')
                    print("Server asking for guess...")

                    send_command(sock, str(next_predicted_31bit))
                    result = receive_until(sock, b'\n')
                    print("Server response:")
                    print(result.decode('utf-8', errors='ignore'))

                    if "Флаг:" in result.decode('utf-8', errors='ignore'):
                        print("SUCCESS! Got the flag!")
                        success = True
                    else:
                        print("Failed to predict correctly")
                    break

            if not success:
                print("All attempts failed. The challenge might require a different approach.")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        sock.close()

if __name__ == "__main__":
    solve_prng_challenge()
